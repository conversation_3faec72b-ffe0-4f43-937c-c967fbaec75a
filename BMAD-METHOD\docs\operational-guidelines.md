# Operational Guidelines

## Coding Standards

### Style Guide & Linter
We will use **Ruff** as the primary tool for both linting and code formatting. A ruff.toml or pyproject.toml [tool.ruff] configuration will be committed to the repository to enforce a consistent style.

### Naming Conventions
All code must follow standard Python [PEP 8](https://peps.python.org/pep-0008/) naming conventions:
* snake_case for variables, functions, methods, and filenames.
* PascalCase for classes.
* UPPER_SNAKE_CASE for constants.

### File Structure
Code must strictly adhere to the layout defined in the "Project Structure" section of this document.

### Unit Test File Organization
Unit tests will be placed in the tests/ directory, mirroring the application's package structure. Test files must be named using the test_*.py convention.

### Asynchronous Operations
All I/O-bound operations (e.g., HTTP requests to the backend, database calls) **must** use the async and await keywords.

### Type Safety
All new functions and methods **must** include full type hints. We will use **Mypy** in our CI/CD pipeline to statically check types and prevent type-related errors.

### Comments & Documentation
Code should be self-documenting where possible. For complex logic, use **Google-style Python docstrings** to explain the "why," not the "what."

### Dependency Management
All project dependencies must be managed through **Poetry** and defined in the pyproject.toml file. The poetry.lock file will be committed to ensure deterministic builds.

## Detailed FastAPI Conventions

### Dependency Injection
Services, configuration objects, and database sessions must be made available to the API layer using FastAPI's dependency injection system (Depends).

### Data Validation
All API request and response bodies must be defined using **Pydantic** models within the app/api/schemas/ directory to leverage FastAPI's automatic data validation and serialization.

### Resource Management
For any resources that require explicit cleanup (like file handles or database connections, though our stack may abstract this), the with statement must be used.

## Testing Strategy

This strategy outlines the different levels of testing that will be implemented for the project.

### Tools
The primary testing framework will be **Pytest**. For testing API endpoints and mocking HTTP requests, we will use FastAPI's TestClient and httpx's built-in mocking capabilities.

### Unit Tests
- **Scope:** Test individual functions and classes in complete isolation. This is particularly important for all translation logic within the TranslationService.
- **Location:** Reside in the tests/ directory, mirroring the application structure.
- **Mocking/Stubbing:** All external dependencies, such as the BackendClientService, will be mocked out so that unit tests never make real network calls.
- **AI Agent Responsibility:** The developer agent is required to generate comprehensive unit tests covering all significant logic paths, edge cases, and error conditions for any new or modified code.

### Integration Tests
- **Scope:** Test the complete request-to-response cycle for each API endpoint, validating the interaction between the API layer, services, and schemas. The actual call to the external MedusaXD API will be mocked at the BackendClientService level.
- **Environment:** These tests will use FastAPI's TestClient to send requests to the application in memory, without needing to run a live server.
- **AI Agent Responsibility:** The developer agent must create integration tests for every API endpoint to verify its behavior, including input validation and error handling.

### End-to-End (E2E) Tests
- **Scope:** Validate the entire application flow by running the containerized bridge and having it connect to a live instance of the MedusaXD API. These tests will verify the translations against the real backend.
- **Tools:** E2E tests will be implemented as Python scripts using the httpx library to act as the client.

### Test Coverage
- **Target:** We will aim for a minimum of **80% line coverage** for unit tests, but the primary focus will be on the quality and effectiveness of the tests over the raw percentage.
- **Measurement:** Code coverage will be measured using the pytest-cov plugin.

### Test Data Management
Reusable test data, such as example API payloads, will be managed using **Pytest fixtures**.

## Error Handling Strategy

### General Approach
The application will use Python's standard exception system. We will define a hierarchy of custom exception classes (e.g., TranslationError, BackendAPIError) to handle specific, known error states with more context than generic exceptions.

### Logging

#### Library/Method
We will use Python's built-in logging module, which is the industry standard.

#### Format
All logs will be emitted in **JSON format**. This structured logging approach allows for easier parsing, searching, and analysis by modern observability platforms.

#### Levels
Standard logging levels will be used:
* DEBUG: Detailed information, typically of interest only when diagnosing problems.
* INFO: Confirmation that things are working as expected.
* WARNING: An indication that something unexpected happened, or a sign of some problem in the near future (e.g., 'disk space low'). The software is still working as expected.
* ERROR: Due to a more serious problem, the software has not been able to perform some function.
* CRITICAL: A serious error, indicating that the program itself may be unable to continue running.

#### Context
To facilitate request tracing, a unique **Correlation ID** will be generated for each incoming request and included in all related log messages.

### Specific Handling Patterns

#### External API Calls
* All calls made with the HTTPX client to the MedusaXD API will have explicit timeouts.
* We will use a library like tenacity to implement a retry mechanism with exponential backoff for transient errors (e.g., 5xx server errors, network issues).
* Client errors (4xx) from the backend will be logged and generally result in a 502 Bad Gateway response from our bridge to our client.

#### Internal Errors / Business Logic Exceptions
We will use FastAPI's custom exception handlers. When a custom exception (e.g., TranslationError) is raised, a specific handler will catch it and return a clear, user-friendly HTTP error response, preventing internal stack traces from being leaked to the client.

## Security Best Practices

### Input Sanitization/Validation
All incoming API requests **must** be validated using Pydantic models. FastAPI's automatic validation of request bodies will be the primary mechanism for preventing malformed data from being processed.

### Secrets Management
Sensitive information, such as API keys for external services or database credentials, **must not** be hardcoded in the source code. They must be loaded exclusively from environment variables via the core/config.py module.

### Dependency Security
The CI/CD pipeline will include a step to automatically scan for vulnerable dependencies using a tool like pip-audit. High-severity vulnerabilities must be addressed before a deployment to production can proceed.

### Authentication/Authorization Checks
* As per the PRD, the Ollama-facing endpoints (/api/*) will not have authentication.
* Any future protected endpoints **must** use a FastAPI dependency that validates the API key generated by our auth service (Epic 3).

### Principle of Least Privilege
The Docker container for the application will be configured to run as a non-root user to limit its privileges on the host system.

### API Security (General)
We will implement FastAPI middleware to add standard security headers (like Content-Security-Policy, X-Content-Type-Options) to all responses to mitigate common web vulnerabilities. HTTPS will be enforced by the hosting environment.

### Error Handling & Information Disclosure
As defined in the Error Handling Strategy, we will use custom exception handlers to ensure that no internal stack traces or sensitive system information are ever leaked in an error response to the client.
