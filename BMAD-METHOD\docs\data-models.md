# Data Models

## API Payload Schemas

These Pydantic models will define the data structures for all API interactions, ensuring robust validation at the application's boundaries.

### Ollama-Format Schemas

_(These models represent the public-facing API of our bridge, mimicking the standard Ollama API format.)_

#### OllamaChatRequest

- **Description:** Represents an incoming chat request from a client.

- **Schema Definition:**
```typescript
interface OllamaChatRequest {
  model: string;
  messages: {
    role: 'system' | 'user' | 'assistant';
    content: string;
    images?: string[]; // base64 encoded images
  }[];
  stream?: boolean;
  // other optional parameters like 'options', 'format', etc.
}
```

#### OllamaChatResponse

- **Description:** Represents a standard, non-streaming response sent back to the client.

- **Schema Definition:**
```typescript
interface OllamaChatResponse {
  model: string;
  created_at: string; // ISO 8601 datetime
  message: {
    role: 'assistant';
    content: string;
  };
  done: true;
  // other metadata fields like 'total_duration', 'eval_count', etc.
}
```

#### OllamaTagsResponse

* **Description:** Represents the response for a model list request.

* **Schema Definition:**
```typescript
interface OllamaTagsResponse {
  models: {
    name: string; // e.g., "llama3:latest"
    modified_at: string; // ISO 8601 datetime
    size: number; // in bytes
  }[];
}
```

### MedusaXD-Format Schemas

_(These models represent the data sent to and received from the backend MedusaXD API, based on the provided OpenAPI specification.)_

#### MedusaChatRequest

- **Description:** Represents the chat request sent to the MedusaXD backend. Conforms to the ChatCompletionRequest schema.

- **Schema Definition:**
```typescript
interface MedusaChatRequest {
  model: string;
  messages: {
    role: 'system' | 'user' | 'assistant' | 'tool' | 'function';
    content: string | object[] | null; // Can be string or multimodal parts
  }[];
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
}
```

#### MedusaModelListResponse

* **Description:** Represents the model list response received from the MedusaXD backend. Conforms to the ModelListResponse schema.

* **Schema Definition:**
```typescript
interface MedusaModelListResponse {
  object: 'list';
  data: {
    id: string;
    object: 'model';
    created: number; // Unix timestamp
    owned_by: string;
  }[];
}
```

## Database Schemas

_(This schema is required to support Epic 3: Authentication Management Service)_

### api_keys Table

* **Purpose:** To securely store hashed API keys for validation.

* **Schema Definition:**
```sql
CREATE TABLE api_keys (
  id SERIAL PRIMARY KEY,
  hashed_key VARCHAR(255) NOT NULL UNIQUE,
  username VARCHAR(50) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP WITH TIME ZONE
);
```
