# Project Structure

This section outlines the definitive directory structure. All new code must be placed in the appropriate location as described below.

```
ollama_bridge/
├── .github/
│   └── workflows/
│       └── ci.yml            # Continuous Integration workflow
├── app/                      # Main application source code
│   ├── __init__.py
│   ├── api/                  # API layer: routers and schemas
│   │   ├── __init__.py
│   │   ├── routers/
│   │   │   ├── __init__.py
│   │   │   ├── ollama_router.py
│   │   │   ├── v1_router.py
│   │   │   └── auth_router.py
│   │   └── schemas/
│   │       ├── __init__.py
│   │       ├── ollama_schemas.py
│   │       └── medusa_schemas.py
│   ├── core/                 # Core logic, config, and clients
│   │   ├── __init__.py
│   │   └── config.py         # Pydantic settings management
│   ├── services/             # Business logic and external service clients
│   │   ├── __init__.py
│   │   ├── translation_service.py
│   │   └── backend_client_service.py
│   └── main.py               # FastAPI application entry point
├── docs/                     # Project documentation (PRD, Arch, etc.)
├── tests/                    # Automated tests
│   ├── __init__.py
│   ├── test_api/
│   └── test_services/
├── .env.example              # Example environment variables
├── .gitignore
├── Dockerfile                # Containerization instructions
├── pyproject.toml            # Project dependencies and metadata (Poetry/Pip)
└── README.md                 # Project overview and setup instructions
```

## Key Directory Descriptions

### app/
Contains all the application's source code.

### app/api/
This package holds everything related to the HTTP interface.

#### app/api/routers/
Contains the FastAPI routers, with each file corresponding to a logical group of endpoints (e.g., ollama_router.py for /api/* endpoints).

#### app/api/schemas/
Contains all Pydantic models used for request/response validation and serialization.

### app/core/
Holds core application logic, with config.py being responsible for loading and providing all configuration settings.

### app/services/
Contains the business logic. translation_service.py will hold the different translation strategies, and backend_client_service.py will manage communication with the MedusaXD API.

### app/main.py
The main entry point that initializes the FastAPI app, includes the routers, and runs the Uvicorn server.

### tests/
Contains all automated tests, mirroring the app/ structure.
