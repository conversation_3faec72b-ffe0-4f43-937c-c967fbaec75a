# API Reference

## External APIs Consumed

This section details the external MedusaXD Public API, which provides all backend functionalities for the bridge.

### MedusaXD Public API

* **Purpose:** A unified, public API providing AI chat, image generation, and web search functionalities.

* **Base URL:** The base URL will be provided via the {MEDUSA_BACKEND_URL} environment variable.

* **Authentication:** No API key is required, as stated in the API documentation.

* **Rate Limits:** Not specified in the provided API documentation.

* **Link to Official Docs:** The OpenAPI spec is available at the /openapi.json path of the service.

* **Key Endpoints Used:**

#### POST /v1/chat/completions

* **Description:** Handles chat completion requests, supporting both streaming and non-streaming responses.

* **Request Body Schema:** Conforms to the ChatCompletionRequest schema defined in the MedusaXD documentation. This will be implemented as a Pydantic model in app/api/schemas/medusa_schemas.py.

* **Success Response Schema:** A JSON object containing the chat completion. The exact structure will be modeled in our Pydantic schemas.

#### POST /v1/images/generations

* **Description:** <PERSON><PERSON> requests to generate images from a text prompt.

* **Request Body Schema:** Conforms to the ImageGenerationRequest schema defined in the MedusaXD documentation. This will be implemented as a Pydantic model.

* **Success Response Schema:** A JSON object containing the generated image data (e.g., URLs or base64 strings).

#### GET /v1/models

* **Description:** Lists all available models from the backend service.

* **Request Body Schema:** None.

* **Success Response Schema:** Conforms to the ModelListResponse schema, containing a list of ModelInfo objects.

#### GET /search

- **Description:** Performs a web search using the backend service.

- **Request Body Schema:** None. Request parameters are sent via URL query string (e.g., ?q=...&engine=...).

- **Success Response Schema:** A JSON object containing the search results.
