"""
V1 router for OpenAI-compatible endpoints.

This module provides endpoints that return data in OpenAI format directly
from the backend without translation to Ollama format.
"""

import logging

from fastapi import APIRouter, Depends, HTTPException

from app.api.schemas.medusa_schemas import MedusaModelListResponse
from app.core.config import Settings, get_settings
from app.services.backend_client_service import (
    BackendClientService,
    BackendConnectionError,
    BackendHTTPError,
    BackendTimeoutError,
    get_backend_client,
)

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/v1",
    tags=["v1"],
    responses={404: {"description": "Not found"}},
)


async def get_backend_client_dependency(settings: Settings = Depends(get_settings)) -> BackendClientService:
    """
    Dependency to get the backend client service.

    Args:
        settings: Application settings

    Returns:
        BackendClientService: Configured backend client
    """
    return get_backend_client(settings)


@router.get(
    "/models",
    response_model=MedusaModelListResponse,
    summary="List Available Models (Direct)",
    description="Get a direct, unfiltered list of models from the backend in OpenAI format",
    responses={
        200: {"description": "Successful model list retrieval"},
        502: {"description": "Backend service error"},
        504: {"description": "Backend service timeout"},
    }
)
async def list_models_direct(
    backend_client: BackendClientService = Depends(get_backend_client_dependency),
) -> MedusaModelListResponse:
    """
    List available models in OpenAI format (direct passthrough).

    This endpoint retrieves the model list from the backend API and returns
    it directly without any translation or filtering. The response follows
    the OpenAI API specification exactly as received from the backend.

    Args:
        backend_client: Client for backend API communication

    Returns:
        MedusaModelListResponse: The model list in OpenAI format

    Raises:
        HTTPException: For various error conditions (502, 504)
    """
    logger.debug("Direct model list request received")

    try:
        # Get model list from backend (direct passthrough)
        logger.debug("Requesting model list from backend")
        medusa_response = await backend_client.get_models()

        logger.info(f"Direct model list retrieved successfully: {len(medusa_response.data)} models")
        return medusa_response

    except BackendTimeoutError as e:
        logger.error(f"Backend timeout during direct model list request: {e}")
        raise HTTPException(
            status_code=504,
            detail="Backend service timeout while retrieving model list"
        ) from e

    except BackendConnectionError as e:
        logger.error(f"Backend connection error during direct model list request: {e}")
        raise HTTPException(
            status_code=502,
            detail="Unable to connect to backend service"
        ) from e

    except BackendHTTPError as e:
        logger.error(f"Backend HTTP error during direct model list request: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service error while retrieving model list"
        ) from e

    except Exception as e:
        logger.error(f"Unexpected error during direct model list request: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error while retrieving model list"
        ) from e
