# Environment Variables Documentation

## Configuration Overview

The bridge application is configured entirely through environment variables, as defined in Story 1.2 of the PRD. All configuration is loaded from environment variables with validation and default values where appropriate.

## Required Configuration Variables

### MEDUSA_BACKEND_URL
- **Description:** Base URL for the MedusaXD backend API
- **Type:** String (URL)
- **Required:** Yes
- **Example:** `https://aiworldcreator.com`

### MODEL_MAPPINGS
- **Description:** JSON mapping of Ollama model names to backend model IDs
- **Type:** JSON String
- **Required:** Yes (can be empty object `{}`)
- **Example:** `{"llama3": "gpt-4o-mini", "codellama": "claude-3-sonnet"}`

## Optional Configuration Variables

### LOG_LEVEL
- **Description:** Logging level for the application
- **Type:** String
- **Default:** `INFO`
- **Valid Values:** `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`
- **Example:** `DEBUG`

### API_TIMEOUT
- **Description:** Timeout in seconds for backend API requests
- **Type:** Integer
- **Default:** `30`
- **Range:** 1-300 seconds
- **Example:** `60`

### APP_NAME
- **Description:** Application name for identification
- **Type:** String
- **Default:** `Ollama-to-OpenAI Bridge`
- **Example:** `My Custom Bridge`

### DEBUG
- **Description:** Enable debug mode
- **Type:** Boolean
- **Default:** `False`
- **Example:** `True`

### MAX_CONCURRENT_REQUESTS
- **Description:** Maximum number of concurrent requests to backend
- **Type:** Integer
- **Default:** `100`
- **Range:** 1-1000
- **Example:** `50`

## Configuration Loading

Configuration is loaded through the `app/core/config.py` module using Pydantic Settings, which provides:

1. **Environment Variable Loading:** Automatic loading from `.env` files and system environment variables
2. **Type Validation:** Automatic type conversion and validation
3. **Default Values:** Sensible defaults for optional settings
4. **Error Handling:** Clear error messages for invalid configuration
5. **Dependency Injection:** Configuration accessible throughout the application via FastAPI's dependency injection

## Example .env File

```bash
# Required Configuration
MEDUSA_BACKEND_URL=https://aiworldcreator.com
MODEL_MAPPINGS={"llama3": "gpt-4o-mini", "codellama": "claude-3-sonnet"}

# Optional Configuration
LOG_LEVEL=INFO
API_TIMEOUT=30
DEBUG=False
MAX_CONCURRENT_REQUESTS=100
APP_NAME=Ollama-to-OpenAI Bridge
```

## Security Considerations

- **Secrets Management:** Sensitive information like API keys must be loaded exclusively from environment variables
- **No Hardcoding:** Configuration values must never be hardcoded in source code
- **Environment Isolation:** Different environments (development, production) should use separate configuration files
