# Epic 2: Expanded API Capabilities

**Goal:** Build upon the foundational bridge by adding new, direct endpoints for model listing, image generation, and web search, transforming the service into a more comprehensive AI gateway.

## Stories

### Story 2.1: Expose Backend Model List Directly

* As a developer, I want to call a /v1/models endpoint on the bridge, so that I can get a direct, unfiltered list of models available from the backend service.

### Story 2.2: List Text-to-Image Models

* As a developer, I want a dedicated /v1/TTI/models endpoint, so that I can easily discover which models are for text-to-image generation.

### Story 2.3 (Revised): Implement Image Generation Endpoint

* As a user, I want to send a detailed request to POST /v1/images/generations, so that I can generate an image with specific parameters.

### Story 2.4 (Revised): Implement Comprehensive Web Search Endpoint

* As a user, I want to call a GET /search endpoint with detailed parameters, so that I can get tailored web search results.

## Detailed Story Breakdown

### Story 2.1: Expose Backend Model List Directly (Priority: P2, Effort: 0.5 days)

**User Story:**
As a developer, I want to call a /v1/models endpoint on the bridge, so that I can get a direct, unfiltered list of models available from the backend service.

**Acceptance Criteria:**
1. GET /v1/models endpoint returns OpenAI-formatted model list
2. Response is direct passthrough from backend
3. No translation or filtering applied
4. Proper error handling for backend failures

**Subtasks:**
- [ ] Create GET /v1/models router endpoint
- [ ] Implement direct passthrough to backend
- [ ] Add error handling
- [ ] Write integration tests

**Dependencies:** Story 1.2

---

### Story 2.2: List Text-to-Image Models (Priority: P2, Effort: 0.5 days)

**User Story:**
As a developer, I want a dedicated /v1/TTI/models endpoint, so that I can easily discover which models are for text-to-image generation.

**Acceptance Criteria:**
1. GET /v1/TTI/models endpoint returns filtered model list
2. Only text-to-image models are included
3. Model filtering logic is configurable
4. Proper error handling and fallbacks

**Subtasks:**
- [ ] Implement model filtering logic
- [ ] Create GET /v1/TTI/models router endpoint
- [ ] Add configuration for TTI model identification
- [ ] Write unit and integration tests

**Dependencies:** Story 2.1

---

### Story 2.3: Implement Image Generation Endpoint (Priority: P2, Effort: 1 day)

**User Story:**
As a user, I want to send a detailed request to POST /v1/images/generations, so that I can generate an image with specific parameters.

**Acceptance Criteria:**
1. POST /v1/images/generations accepts OpenAI-compatible requests
2. Request is forwarded to backend image generation API
3. Response includes generated image data
4. Proper error handling for generation failures
5. Support for various image parameters (size, quality, etc.)

**Subtasks:**
- [ ] Create image generation request/response schemas
- [ ] Implement POST /v1/images/generations endpoint
- [ ] Add backend image generation client method
- [ ] Implement parameter validation
- [ ] Add comprehensive error handling
- [ ] Write integration tests

**Dependencies:** Story 1.2

---

### Story 2.4: Implement Web Search Endpoint (Priority: P3, Effort: 1 day)

**User Story:**
As a user, I want to call a GET /search endpoint with detailed parameters, so that I can get tailored web search results.

**Acceptance Criteria:**
1. GET /search endpoint accepts query parameters
2. Request is forwarded to backend search API
3. Search results are returned in appropriate format
4. Support for search engine selection
5. Proper error handling for search failures

**Subtasks:**
- [ ] Create search request/response schemas
- [ ] Implement GET /search endpoint with query parameters
- [ ] Add backend search client method
- [ ] Implement parameter validation
- [ ] Add error handling
- [ ] Write integration tests

**Dependencies:** Story 1.2
