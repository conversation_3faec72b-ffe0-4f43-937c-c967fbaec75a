# Infrastructure and Deployment Overview

## Cloud Provider(s)
The architecture is cloud-agnostic. However, the initial deployment will target a major cloud provider (e.g., AWS, GCP, Azure) that offers a simple, serverless container platform.

## Core Services Used
We will use a **Serverless Container Platform** (e.g., AWS App Runner or Google Cloud Run). This choice abstracts away the complexity of managing servers and clusters, aligning with the project's lightweight nature.

## Infrastructure as Code (IaC)
We will use **Terraform** to define and manage all necessary cloud resources (like the container service itself). All Terraform code will reside in the infra/ directory.

## Deployment Strategy
A **CI/CD pipeline** will be implemented using GitHub Actions. Every push to the main branch will automatically trigger the pipeline to build, test, and deploy the new version of the application.

## Environments
We will start with two environments:

### Development
The local machine environment where developers run the service.

### Production
The live environment hosted on the cloud provider.

## Environment Promotion
A push to the main branch, after all automated tests have passed, will trigger a direct deployment to the Production environment.

## Rollback Strategy
We will rely on the managed container service's built-in rollback capabilities. A rollback can be triggered manually via the cloud provider's console or an API call to redeploy the previous stable container image.
