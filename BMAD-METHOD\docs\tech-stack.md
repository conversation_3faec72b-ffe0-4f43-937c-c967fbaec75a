# Technology Stack

This list is the definitive standard for the project.

| **Category**        | **Technology** | **Version / Details** | **Description / Purpose**                                                                | **Justification (Optional)**                                                                                          |
| ------------------- | -------------- | --------------------- | ---------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------- |
| **Languages**       | Python         | 3.11                  | Primary language for the application backend.                                            | Modern, well-supported, and excellent for API development with a rich ecosystem.                                      |
| **Frameworks**      | FastAPI        | Latest                | High-performance ASGI framework for building APIs.                                       | Provides automatic data validation, dependency injection, and interactive API docs.                                   |
| **Web Server**      | Uvicorn        | Latest                | Lightning-fast ASGI server, required to run FastAPI.                                     | The standard and recommended server for FastAPI applications.                                                         |
| **Databases**       | SQLite         | 3.x                   | Lightweight, file-based SQL database for storing hashed API keys.                        | Simple, no external setup required, and perfectly sufficient for the limited persistence needs of Epic 3.             |
| **Testing**         | Pytest         | Latest                | Powerful and flexible testing framework for Python.                                      | A mature and feature-rich framework that is the de-facto standard for testing in the Python community.                |
| **HTTP Client**     | HTTPX          | Latest                | A fully featured asynchronous HTTP client for Python.                                    | Required for making non-blocking calls to the backend MedusaXD API, which is essential for a high-performance bridge. |
| **Infrastructure**  | Docker         | Latest                | Platform for developing, shipping, and running applications in containers.               | Ensures consistency between development and production environments and simplifies deployment.                        |
| **CI/CD**           | GitHub Actions | N/A                   | Automation platform for build, test, and deployment pipelines.                           | Tightly integrated with GitHub for seamless continuous integration and deployment workflows.                          |
| **Dep. Management** | Poetry         | Latest                | Tool for Python dependency management and packaging.                                     | Provides deterministic dependency resolution and a superior workflow compared to traditional requirements.txt files.  |
