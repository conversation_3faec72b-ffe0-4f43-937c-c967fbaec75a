# Epic 1: Foundational Bridge & Core Translation Service

**Goal:** Establish the core FastAPI application, implement the primary Ollama-to-OpenAI translation for chat and model listing, and include all necessary configurations to create a testable, value-delivering product.

## Stories

### Story 1.1: Project Setup & Health Check

* As a developer, I want a basic FastAPI application structure with a /health endpoint, so that I have a verifiable foundation to build upon and can monitor the service's status.

### Story 1.2: Implement Configuration System

* As a developer, I want to configure the bridge with a backend URL, API key, and model mappings from environment variables, so that I can easily deploy and manage the service without changing code.

### Story 1.3: Non-Streaming Chat Translation

* As a user, I want to send a non-streaming Ollama-formatted chat request to the bridge, so that it gets translated and forwarded to the OpenAI-compatible backend, and the response is translated back to me.

### Story 1.4: Streaming Chat Translation

* As a user, I want to send a streaming Ollama-formatted chat request to the bridge, so that I receive the response token-by-token in real-time.

### Story 1.5: Model List Translation

* As a user, I want to request the model list from the bridge in the Ollama format, so that my client application can display the available models from the backend.

### Story 1.6: Implement Model Name Mapping

* As a user, I want the bridge to use a configured map to translate model names, so that when I request a generic model name, it is sent to the backend as the specific required model.

## Detailed Story Breakdown

### Story 1.1: Project Setup & Health Check (Priority: P0, Effort: 1 day)

**User Story:**
As a developer, I want a basic FastAPI application structure with a /health endpoint, so that I have a verifiable foundation to build upon and can monitor the service's status.

**Acceptance Criteria:**
1. FastAPI application initializes successfully
2. GET /health endpoint returns 200 OK with status information
3. Application follows the project structure defined in architecture.md
4. Basic logging is configured and working
5. Application can be started locally with `uvicorn app.main:app --reload`

**Subtasks:**
- [ ] Initialize Poetry project with pyproject.toml
- [ ] Create project directory structure per architecture specification
- [ ] Set up FastAPI application in app/main.py
- [ ] Implement health check endpoint in app/api/routers/
- [ ] Configure basic logging
- [ ] Create .env.example file
- [ ] Write basic README with setup instructions
- [ ] Add .gitignore file

**Dependencies:** None

---

### Story 1.2: Implement Configuration System (Priority: P0, Effort: 1 day)

**User Story:**
As a developer, I want to configure the bridge with a backend URL, API key, and model mappings from environment variables, so that I can easily deploy and manage the service without changing code.

**Acceptance Criteria:**
1. Configuration loads from environment variables
2. Required config: MEDUSA_BACKEND_URL, MODEL_MAPPINGS
3. Optional config: LOG_LEVEL, API_TIMEOUT
4. Configuration validation with clear error messages
5. Default values provided for optional settings
6. Configuration accessible via dependency injection

**Subtasks:**
- [ ] Create app/core/config.py with Pydantic Settings
- [ ] Define configuration schema with validation
- [ ] Implement model mapping configuration (JSON format)
- [ ] Add configuration dependency injection setup
- [ ] Create comprehensive .env.example
- [ ] Add configuration validation tests
- [ ] Document all configuration options

**Dependencies:** Story 1.1

---

### Story 1.3: Non-Streaming Chat Translation (Priority: P0, Effort: 2 days)

**User Story:**
As a user, I want to send a non-streaming Ollama-formatted chat request to the bridge, so that it gets translated and forwarded to the OpenAI-compatible backend, and the response is translated back to me.

**Acceptance Criteria:**
1. POST /api/chat endpoint accepts Ollama chat format
2. Request is translated to OpenAI format for backend
3. Backend response is translated back to Ollama format
4. Non-streaming responses work correctly
5. Error handling for backend failures
6. Request/response logging for debugging

**Subtasks:**
- [ ] Create Ollama chat request/response Pydantic schemas
- [ ] Create OpenAI chat request/response Pydantic schemas
- [ ] Implement translation service with strategy pattern
- [ ] Create backend client service with HTTPX
- [ ] Implement POST /api/chat router endpoint
- [ ] Add comprehensive error handling
- [ ] Write unit tests for translation logic
- [ ] Write integration tests for endpoint

**Dependencies:** Story 1.2

---

### Story 1.4: Streaming Chat Translation (Priority: P1, Effort: 2 days)

**User Story:**
As a user, I want to send a streaming Ollama-formatted chat request to the bridge, so that I receive the response token-by-token in real-time.

**Acceptance Criteria:**
1. POST /api/chat with stream=true parameter works
2. Streaming responses are properly formatted for Ollama
3. Each chunk is translated from OpenAI to Ollama format
4. Connection handling for interrupted streams
5. Proper cleanup of streaming connections
6. Performance meets <100ms first token requirement

**Subtasks:**
- [ ] Implement streaming response handling in backend client
- [ ] Create streaming translation logic
- [ ] Add streaming support to /api/chat endpoint
- [ ] Implement proper connection management
- [ ] Add streaming error handling
- [ ] Write streaming integration tests
- [ ] Performance testing for streaming latency

**Dependencies:** Story 1.3

---

### Story 1.5: Model List Translation (Priority: P1, Effort: 1 day)

**User Story:**
As a user, I want to request the model list from the bridge in the Ollama format, so that my client application can display the available models from the backend.

**Acceptance Criteria:**
1. GET /api/tags endpoint returns Ollama-formatted model list
2. Backend /v1/models response is translated correctly
3. Model metadata is preserved where possible
4. Error handling for backend model list failures
5. Caching of model list for performance (optional)

**Subtasks:**
- [ ] Create model list Pydantic schemas (Ollama & OpenAI)
- [ ] Implement model list translation logic
- [ ] Create GET /api/tags router endpoint
- [ ] Add backend model list client method
- [ ] Implement error handling
- [ ] Write unit and integration tests

**Dependencies:** Story 1.2

---

### Story 1.6: Implement Model Name Mapping (Priority: P1, Effort: 1 day)

**User Story:**
As a user, I want the bridge to use a configured map to translate model names, so that when I request a generic model name, it is sent to the backend as the specific required model.

**Acceptance Criteria:**
1. Model name mapping works in chat requests
2. Model name mapping works in model list responses
3. Configuration supports flexible mapping rules
4. Fallback behavior for unmapped models
5. Clear error messages for invalid model names

**Subtasks:**
- [ ] Extend configuration to support model mappings
- [ ] Implement model name translation logic
- [ ] Integrate mapping into chat translation
- [ ] Integrate mapping into model list translation
- [ ] Add validation for model mapping configuration
- [ ] Write comprehensive tests for mapping logic

**Dependencies:** Stories 1.3, 1.5
