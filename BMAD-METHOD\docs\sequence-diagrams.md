# Core Workflow / Sequence Diagrams

## 1. Non-Streaming Chat Completion Workflow

This diagram illustrates the "happy path" for a standard, non-streaming chat request, showing how it flows through the internal components of the bridge.

```mermaid
sequenceDiagram
    participant Client
    participant API_Layer as API Layer (Routers)
    participant Translator as Translation Service
    participant Backend_Client as Backend Client Service
    participant MedusaXD_API as MedusaXD API
    
    Client->>+API_Layer: POST /api/chat (OllamaChatRequest)
    API_Layer->>+Translator: process_chat_request(request_data)
    Translator->>Translator: Adapt Ollama request to MedusaXD format
    Translator->>+Backend_Client: send_chat_request(MedusaChatRequest)
    Backend_Client->>+MedusaXD_API: POST /v1/chat/completions
    MedusaXD_API-->>-Backend_Client: MedusaChatResponse
    Backend_Client-->>-Translator: response
    Translator->>Translator: Adapt MedusaXD response to Ollama format
    Translator-->>-API_Layer: OllamaChatResponse
    API_Layer-->>-Client: 200 OK (OllamaChatResponse)
```

## 2. Streaming Chat Completion Workflow

This diagram shows how a streaming request is handled, with the bridge translating and forwarding each data chunk as it arrives.

```mermaid
sequenceDiagram
    participant Client
    participant API_Layer as API Layer (Routers)
    participant Translator as Translation Service
    participant Backend_Client as Backend Client Service
    participant MedusaXD_API as MedusaXD API
    
    Client->>+API_Layer: POST /api/chat (stream=true)
    API_Layer->>+Translator: process_streaming_chat(request_data)
    Translator->>+Backend_Client: send_streaming_chat_request(MedusaChatRequest)
    Backend_Client->>+MedusaXD_API: POST /v1/chat/completions (stream=true)
    MedusaXD_API-->>-Backend_Client: Stream of data chunks
    
    loop For each chunk
        Backend_Client-->>Translator: chunk
        Translator->>Translator: Adapt chunk to Ollama format
        Translator-->>API_Layer: translated_chunk
        API_Layer-->>Client: Send chunk
    end
```

## 3. API Key Generation & Validation Workflow

This diagram illustrates the two main authentication flows defined in Epic 3.

```mermaid
sequenceDiagram
    box "Key Generation"
        participant Client_Admin as Admin Client
        participant Auth_Router as Auth Router
        participant DB as Database
        
        Client_Admin->>+Auth_Router: POST /v1/auth/generate-key
        Auth_Router->>Auth_Router: Generate new key & hash
        Auth_Router->>+DB: Store hashed_key
        DB-->>-Auth_Router: Success
        Auth_Router-->>-Client_Admin: 200 OK (plaintext_key)
    end
    
    box "Key Validation"
        participant App_Client as Application Client
        participant V1_Router as V1 Router (Protected)
        
        App_Client->>+V1_Router: GET /v1/images/generations (Authorization: Bearer <key>)
        V1_Router->>+Auth_Router: validate_key(key)
        Auth_Router->>+DB: Query for hashed_key
        DB-->>-Auth_Router: Found / Not Found
        Auth_Router-->>-V1_Router: Validation Result (Valid/Invalid)
        
        alt Key is Valid
            V1_Router->>V1_Router: Process request...
        else Key is Invalid
            V1_Router-->>-App_Client: 401 Unauthorized
        end
    end
```
